mod api;
mod db;
mod services;

use axum::http::Method;
use rs_firebase_admin_sdk::{
    auth::token::{
        cache::HttpCache, crypto::JwtRsaPubKey, LiveTokenVerifier
    }, credentials_provider, App
};
use std::{collections::BTreeMap, sync::Arc, env};
use tower_http::cors::CorsLayer;
use tower_http::trace::TraceLayer;

use services::spicedb_service::SpiceDBService;

type AppVerifier = LiveTokenVerifier<HttpCache<reqwest::Client, BTreeMap<String, JwtRsaPubKey>>>;

#[derive(Clone)]
pub struct ServerState {
    pub db: db::Database,
    pub verifier: Arc<AppVerifier>,
    pub spicedb: Option<SpiceDBService>,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Load .env file if it exists
    let _ = dotenv::dotenv();

    // Initialize tracing
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG)
        .init();

    // Initialize database
    let db = db::Database::new().await?;
    tracing::info!("Database connected");

    // Initialize SpiceDB service
    let spicedb_service = match initialize_spicedb_service().await {
        Ok(service) => {
            tracing::info!("SpiceDB service initialized successfully");
            Some(service)
        }
        Err(e) => {
            tracing::warn!("Failed to initialize SpiceDB service: {}. Continuing without SpiceDB integration.", e);
            None
        }
    };

    // Seed default roles if needed (after SpiceDB initialization)
    if let Err(e) = db::seed::seed_default_roles(&db, spicedb_service.as_ref()).await {
        tracing::warn!("Failed to seed default roles: {}", e);
    }

    let verifier = Arc::new(
        App::live(credentials_provider().await?)
            .await.expect("cannot receive google live app")
            .id_token_verifier()
            .await.expect("cannot receive google live token verifier")
    );

    // Configure CORS with specific origin for credentials
    let frontend_url = std::env::var("FRONTEND_URL")
        .unwrap_or_else(|_| "http://localhost:5000".to_string());

    // When using credentials, we need to specify exact headers and origins
    let cors = CorsLayer::new()
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers([
            axum::http::header::AUTHORIZATION,
            axum::http::header::CONTENT_TYPE,
            axum::http::header::ACCEPT,
        ])
        .allow_origin(frontend_url.parse::<axum::http::HeaderValue>().unwrap())
        .allow_credentials(true);

    // Build our application with routes
    let app = api::create_router(ServerState {
        db,
        verifier,
        spicedb: spicedb_service
    })
        .layer(TraceLayer::new_for_http())
        .layer(cors);

    // Get port from environment or use default
    let port = std::env::var("PORT")
        .unwrap_or_else(|_| "3000".to_string())
        .parse::<u16>()
        .unwrap_or(3000);

    let addr = std::net::SocketAddr::from(([0, 0, 0, 0], port));
    tracing::info!("Listening on {}", addr);

    // Run our app with hyper
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

/// Initialize SpiceDB service with mTLS or insecure connection
async fn initialize_spicedb_service() -> anyhow::Result<SpiceDBService> {
    let endpoint = env::var("SPICEDB_ENDPOINT")
        .unwrap_or_else(|_| "http://localhost:50051".to_string());

    let preshared_key = env::var("SPICEDB_PRESHARED_KEY")
        .unwrap_or_else(|_| "somerandomkeyhere".to_string());

    // Check if mTLS certificates are configured
    let cert_path = env::var("SPICEDB_CERT_PATH").ok();
    let key_path = env::var("SPICEDB_KEY_PATH").ok();
    let ca_path = env::var("SPICEDB_CA_PATH").ok();

    match (cert_path, key_path, ca_path) {
        (Some(cert), Some(key), Some(ca)) => {
            tracing::info!("Initializing SpiceDB with mTLS");
            SpiceDBService::new(&endpoint, &cert, &key, &ca, &preshared_key).await
        }
        _ => {
            tracing::info!("Initializing SpiceDB with insecure connection");
            SpiceDBService::new_insecure(&endpoint, &preshared_key).await
        }
    }
}